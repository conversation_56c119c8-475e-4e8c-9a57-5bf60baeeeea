-- 调试版瞬移脚本 - 帮助诊断进程连接问题
-- 支持32位和64位系统

-- 配置
local 目标包名 = "ccz.locojoy.mini.mt34.joysdk"

-- 调试函数：检查应用是否运行
local function 检查应用状态()
    print("=== 应用状态检查 ===")
    
    -- 检查不同的包名变体
    local 可能的包名 = {
        "ccz.locojoy.mini.mt34.joysdk",
        "ccz.locojoy.mini.mt34",
        "ccz.locojoy.mini",
        "ccz.locojoy"
    }
    
    for i, 包名 in ipairs(可能的包名) do
        print(string.format("检查包名 %d: %s", i, 包名))
        
        -- 尝试获取模块句柄
        local 模块句柄 = app.GetModuleHandle(包名, "libgame.so")
        if 模块句柄 and 模块句柄 ~= 0 then
            print(string.format("✅ 找到应用: %s", 包名))
            print(string.format("   libgame.so 基址: 0x%X", 模块句柄))
            return 包名
        else
            print(string.format("❌ 未找到: %s", 包名))
        end
    end
    
    return nil
end

-- 测试内存读取
local function 测试内存读取(包名)
    print("\n=== 内存读取测试 ===")
    
    local 模块句柄 = app.GetModuleHandle(包名, "libgame.so")
    if not 模块句柄 or 模块句柄 == 0 then
        print("❌ 无法获取模块句柄")
        return false
    end
    
    print(string.format("模块基址: 0x%X", 模块句柄))
    
    -- 测试读取BSS地址
    local bss地址 = 模块句柄 + 0xD37684
    print(string.format("BSS地址: 0x%X", bss地址))
    
    local 读取值 = app.MemoryRead(包名, bss地址, "u32")
    if 读取值 then
        print(string.format("✅ BSS读取成功: 0x%X", 读取值))
        return true
    else
        print("❌ BSS读取失败")
        return false
    end
end

-- 瞬移函数 - 带详细调试信息
function 瞬移调试版(x坐标, y坐标)
    print(string.format("\n=== 开始瞬移到坐标: (%.1f, %.1f) ===", x坐标, y坐标))
    
    -- 1. 检查应用状态
    local 正确包名 = 检查应用状态()
    if not 正确包名 then
        print("\n❌ 未找到目标应用，请确认:")
        print("1. 应用是否正在运行")
        print("2. 应用是否在前台")
        print("3. 包名是否正确")
        return false
    end
    
    -- 2. 测试内存读取
    if not 测试内存读取(正确包名) then
        print("\n❌ 内存读取测试失败")
        return false
    end
    
    -- 3. 执行瞬移
    print("\n=== 执行瞬移操作 ===")
    
    local 模块句柄 = app.GetModuleHandle(正确包名, "libgame.so")
    local bss地址 = 模块句柄 + 0xD37684
    local 读取bss值 = app.MemoryRead(正确包名, bss地址, "u32")
    local 基地址 = 读取bss值 ~= 0 and 读取bss值 or bss地址
    
    print(string.format("基地址: 0x%X", 基地址))
    
    -- 第一级偏移
    local 偏移1 = app.MemoryRead(正确包名, 基地址 + 0x78, "u32")
    if not 偏移1 then
        print("❌ 第一级偏移读取失败")
        return false
    end
    print(string.format("第一级偏移: 0x%X", 偏移1))
    
    -- 第二级偏移
    local 偏移2 = app.MemoryRead(正确包名, 偏移1 + 0x11C, "u32")
    if not 偏移2 then
        print("❌ 第二级偏移读取失败")
        return false
    end
    print(string.format("第二级偏移: 0x%X", 偏移2))
    
    -- 计算坐标地址
    local x地址 = 偏移2 + 0x20 - 20
    local y地址 = 偏移2 + 0x20 - 16
    local x实际值 = x坐标 * 24
    local y实际值 = y坐标 * 16
    
    print(string.format("X坐标地址: 0x%X, 值: %.0f", x地址, x实际值))
    print(string.format("Y坐标地址: 0x%X, 值: %.0f", y地址, y实际值))
    
    -- 修改坐标
    local x结果 = app.MemoryWrite(正确包名, x地址, x实际值, "f32")
    local y结果 = app.MemoryWrite(正确包名, y地址, y实际值, "f32")
    
    if x结果 and y结果 then
        print("✅ 坐标修改成功")
        
        -- 验证修改结果
        local 验证x = app.MemoryRead(正确包名, x地址, "f32")
        local 验证y = app.MemoryRead(正确包名, y地址, "f32")
        
        if 验证x and 验证y then
            print(string.format("验证结果: X=%.1f, Y=%.1f", 验证x, 验证y))
        end
        
        -- 执行点击
        if _G.sleep then _G.sleep(1500) end
        if _G.tap then _G.tap(648, 362) end
        if _G.sleep then _G.sleep(500) end
        
        print("🎉 瞬移完成!")
        return true
    else
        print("❌ 坐标修改失败")
        return false
    end
end

-- 简化版瞬移函数
function 瞬移(x坐标, y坐标)
    -- 直接使用找到的正确包名
    local 正确包名 = 检查应用状态()
    if not 正确包名 then
        print("❌ 未找到目标应用")
        return false
    end
    
    local 模块句柄 = app.GetModuleHandle(正确包名, "libgame.so")
    local bss地址 = 模块句柄 + 0xD37684
    local 读取bss值 = app.MemoryRead(正确包名, bss地址, "u32")
    local 基地址 = 读取bss值 ~= 0 and 读取bss值 or bss地址
    local 偏移1 = app.MemoryRead(正确包名, 基地址 + 0x78, "u32")
    local 偏移2 = app.MemoryRead(正确包名, 偏移1 + 0x11C, "u32")
    
    local x实际值 = x坐标 * 24
    local y实际值 = y坐标 * 16
    
    app.MemoryWrite(正确包名, 偏移2 + 0x20 - 20, x实际值, "f32")
    app.MemoryWrite(正确包名, 偏移2 + 0x20 - 16, y实际值, "f32")
    
    if _G.sleep then _G.sleep(1500) end
    if _G.tap then _G.tap(648, 362) end
    if _G.sleep then _G.sleep(500) end
    
    print(string.format("✅ 瞬移到: (%.0f, %.0f)", x实际值, y实际值))
    return true
end

-- 使用说明
print("=== 调试版瞬移脚本 ===")
print("可用函数:")
print("  瞬移调试版(x, y) - 详细调试信息")
print("  瞬移(x, y)       - 简化版本")
print("\n请先确保目标应用正在运行，然后调用:")
print("瞬移调试版(80, 50)")

-- 自动运行调试版本
瞬移调试版(80, 50)
