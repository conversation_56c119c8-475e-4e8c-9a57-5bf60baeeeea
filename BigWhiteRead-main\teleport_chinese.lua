-- BigWhiteRead瞬移模块 - 纯中文版
-- 使用中文变量名和函数名

require("read")

-- 配置
local 目标包名 = "ccz.locojoy.mini.mt34.joysdk"
local 已连接 = false
local 进程号 = 0

-- 初始化连接
local function 初始化连接()
    if not 已连接 then
        进程号 = Mem.GetPID(目标包名)
        if 进程号 == 0 then
            print("❌ 连接失败，请确认游戏正在运行")
            return false
        end
        已连接 = true
        print("✅ 连接成功，PID: " .. 进程号)
    end
    return true
end

-- 核心瞬移函数
function TeleportModule.瞬移到(x坐标, y坐标)
    if not initConnection() then
        return false
    end
    
    -- 获取游戏模块基址
    local moduleBase = Mem.GetModuleBase("libgame.so")
    if moduleBase == 0 then
        return false
    end
    
    -- 计算BSS地址
    local bssAddr = moduleBase + 0xD37684
    local bssValue = Mem.GetDword(bssAddr)
    local baseAddr = (bssValue ~= 0 and bssValue < 0x80000000) and bssValue or bssAddr
    
    -- 多级指针偏移
    local offset1 = Mem.GetDword(baseAddr + 0x78)
    if not offset1 or offset1 == 0 then return false end
    
    local offset2 = Mem.GetDword(offset1 + 0x11C)
    if not offset2 or offset2 == 0 then return false end
    
    -- 计算坐标地址并修改
    local xAddr = offset2 + 0x20 - 20
    local yAddr = offset2 + 0x20 - 16
    local xValue = x坐标 * 24
    local yValue = y坐标 * 16
    
    Mem.WriteFloat(xAddr, xValue)
    Mem.WriteFloat(yAddr, yValue)
    
    print(string.format("✅ 瞬移成功: (%.0f, %.0f)", xValue, yValue))
    return true
end

-- 简化调用函数
function TeleportModule.瞬移(x, y)
    return TeleportModule.瞬移到(x, y)
end

-- 获取当前坐标
function TeleportModule.获取坐标()
    if not initConnection() then
        return nil, nil
    end
    
    local moduleBase = Mem.GetModuleBase("libgame.so")
    if moduleBase == 0 then return nil, nil end
    
    local bssAddr = moduleBase + 0xD37684
    local bssValue = Mem.GetDword(bssAddr)
    local baseAddr = (bssValue ~= 0 and bssValue < 0x80000000) and bssValue or bssAddr
    
    local offset1 = Mem.GetDword(baseAddr + 0x78)
    if not offset1 or offset1 == 0 then return nil, nil end
    
    local offset2 = Mem.GetDword(offset1 + 0x11C)
    if not offset2 or offset2 == 0 then return nil, nil end
    
    local xAddr = offset2 + 0x20 - 20
    local yAddr = offset2 + 0x20 - 16
    
    local xValue = Mem.GetFloat(xAddr)
    local yValue = Mem.GetFloat(yAddr)
    
    if xValue and yValue then
        return xValue / 24, yValue / 16
    end
    
    return nil, nil
end

-- 批量瞬移
function TeleportModule.批量瞬移(坐标列表, 间隔时间)
    间隔时间 = 间隔时间 or 1000
    local successCount = 0
    
    for i, coord in ipairs(坐标列表) do
        if TeleportModule.瞬移(coord[1], coord[2]) then
            successCount = successCount + 1
        end
        
        if i < #坐标列表 and _G.sleep then
            _G.sleep(间隔时间)
        end
    end
    
    print(string.format("批量瞬移完成: %d/%d", successCount, #坐标列表))
    return successCount
end

-- 瞬移到随机位置
function TeleportModule.随机瞬移(x范围, y范围)
    x范围 = x范围 or {0, 100}
    y范围 = y范围 or {0, 100}
    
    local randomX = math.random(x范围[1], x范围[2])
    local randomY = math.random(y范围[1], y范围[2])
    
    return TeleportModule.瞬移(randomX, randomY)
end

-- 保存当前位置
local savedPosition = nil
function TeleportModule.保存位置()
    local x, y = TeleportModule.获取坐标()
    if x and y then
        savedPosition = {x, y}
        print(string.format("✅ 位置已保存: (%.1f, %.1f)", x, y))
        return true
    end
    return false
end

-- 回到保存的位置
function TeleportModule.回到保存位置()
    if savedPosition then
        return TeleportModule.瞬移(savedPosition[1], savedPosition[2])
    else
        print("❌ 没有保存的位置")
        return false
    end
end

-- 导出全局函数，方便直接调用
_G.瞬移 = TeleportModule.瞬移
_G.瞬移到 = TeleportModule.瞬移到
_G.获取坐标 = TeleportModule.获取坐标
_G.批量瞬移 = TeleportModule.批量瞬移
_G.随机瞬移 = TeleportModule.随机瞬移
_G.保存位置 = TeleportModule.保存位置
_G.回到保存位置 = TeleportModule.回到保存位置

-- 英文别名（兼容性）
_G.teleport = TeleportModule.瞬移
_G.tp = TeleportModule.瞬移
_G.getPos = TeleportModule.获取坐标

-- 使用说明
print("=== BigWhiteRead瞬移模块 - 中文版 ===")
print("✅ 模块加载完成")
print("")
print("🎯 基础功能:")
print("  瞬移(x, y)           - 瞬移到指定坐标")
print("  获取坐标()           - 获取当前坐标")
print("")
print("🚀 高级功能:")
print("  批量瞬移({{x1,y1}, {x2,y2}})  - 批量瞬移")
print("  随机瞬移({0,100}, {0,100})    - 随机位置瞬移")
print("  保存位置()                    - 保存当前位置")
print("  回到保存位置()                - 回到保存的位置")
print("")
print("💡 使用示例:")
print("  瞬移(50, 50)")
print("  local x, y = 获取坐标()")
print("  批量瞬移({{10,20}, {30,40}, {50,60}})")
print("  随机瞬移()")

return TeleportModule
