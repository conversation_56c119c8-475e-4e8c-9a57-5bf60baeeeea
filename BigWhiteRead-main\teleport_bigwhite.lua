-- BigWhiteRead瞬移脚本 - 精简版
require("read")

-- 配置
local packageName = "ccz.locojoy.mini.mt34.joysdk"

-- 连接到进程
local pid = Mem.GetPID(packageName)
if pid == 0 then
    print("❌ 连接失败，请确认游戏正在运行")
    return
end

print("✅ 连接成功，PID: " .. pid)

-- 瞬移函数 - 使用BigWhiteRead的强大API
function teleport(x, y)
    print(string.format("开始瞬移到坐标: (%.1f, %.1f)", x, y))
    
    -- 获取libgame.so模块基址
    local moduleBase = Mem.GetModuleBase("libgame.so")
    if moduleBase == 0 then
        print("❌ 获取libgame.so模块失败")
        return false
    end
    
    print(string.format("模块基址: 0x%X", moduleBase))
    
    -- 计算BSS地址
    local bssAddress = moduleBase + 0xD37684
    
    -- 读取BSS地址的值
    local bssValue = Mem.GetDword(bssAddress)
    print(string.format("BSS值: 0x%X", bssValue or 0))

    -- 修复基地址计算逻辑
    local baseAddress
    if bssValue and bssValue ~= 0 and bssValue < 0x80000000 then
        -- BSS值有效且为正常地址范围
        baseAddress = bssValue
        print("使用BSS值作为基地址")
    else
        -- BSS值无效，直接使用BSS地址
        baseAddress = bssAddress
        print("使用BSS地址作为基地址")
    end

    print(string.format("基地址: 0x%X", baseAddress))
    
    -- 智能指针读取函数
    local function readPointer(address)
        -- 先尝试32位读取（更常见）
        local value = Mem.GetDword(address)
        if value and value ~= 0 and value < 0x80000000 then
            return value
        end

        -- 如果32位读取失败或值异常，尝试64位
        local value64 = Mem.GetPtr64(address)
        if value64 and value64 ~= 0 and value64 < 0x7FFFFFFF then
            return value64
        end

        -- 最后尝试32位指针读取
        local value32 = Mem.GetPtr32(address)
        if value32 and value32 ~= 0 then
            return value32
        end

        return nil
    end

    -- 第一级偏移
    local offset1 = readPointer(baseAddress + 0x78)
    if not offset1 then
        print("❌ 读取第一级偏移失败")
        print(string.format("尝试地址: 0x%X", baseAddress + 0x78))
        return false
    end
    print(string.format("第一级偏移: 0x%X", offset1))

    -- 第二级偏移
    local offset2 = readPointer(offset1 + 0x11C)
    if not offset2 then
        print("❌ 读取第二级偏移失败")
        print(string.format("尝试地址: 0x%X", offset1 + 0x11C))
        return false
    end
    print(string.format("第二级偏移: 0x%X", offset2))
    
    -- 计算实际坐标值
    local xValue = x * 24
    local yValue = y * 16
    
    -- 计算坐标地址
    local xAddress = offset2 + 0x20 - 20
    local yAddress = offset2 + 0x20 - 16
    
    print(string.format("X坐标地址: 0x%X, 值: %.0f", xAddress, xValue))
    print(string.format("Y坐标地址: 0x%X, 值: %.0f", yAddress, yValue))
    
    -- 修改X坐标
    local xResult = Mem.WriteFloat(xAddress, xValue)
    if not xResult then
        print("❌ 修改X坐标失败")
        return false
    end
    
    -- 修改Y坐标
    local yResult = Mem.WriteFloat(yAddress, yValue)
    if not yResult then
        print("❌ 修改Y坐标失败")
        return false
    end
    
    print("✅ 坐标修改成功")
    
    -- 验证修改结果
    local verifyX = Mem.GetFloat(xAddress)
    local verifyY = Mem.GetFloat(yAddress)
    
    if verifyX and verifyY then
        print(string.format("验证结果: X=%.1f, Y=%.1f", verifyX/24, verifyY/16))
    end
    
    print(string.format("🎉 瞬移成功: (%.0f, %.0f)", xValue, yValue))
    
    -- 执行点击操作
    if _G.sleep then _G.sleep(1500) end
    if _G.tap then _G.tap(648, 362) end
    if _G.sleep then _G.sleep(500) end
    
    return true
end

-- 使用BigWhiteRead的搜索功能找坐标地址（备用方案）
function searchCoordinates(currentX, currentY)
    print("=== 使用BigWhiteRead搜索坐标地址 ===")
    print(string.format("搜索当前坐标: X=%.1f, Y=%.1f", currentX, currentY))
    
    local xMemValue = currentX * 24
    local yMemValue = currentY * 16
    
    print(string.format("搜索内存值: X=%.0f, Y=%.0f", xMemValue, yMemValue))
    
    -- 使用BigWhiteRead的搜索功能
    local xResults = Mem.Search_FLOAT(xMemValue, 0)  -- 0表示搜索所有内存区域
    local yResults = Mem.Search_FLOAT(yMemValue, 0)
    
    print(string.format("X坐标搜索结果: %d个", xResults.count))
    print(string.format("Y坐标搜索结果: %d个", yResults.count))
    
    if xResults.count > 0 and xResults.count <= 20 then
        print("X坐标可能的地址:")
        for i = 1, math.min(xResults.count, 5) do  -- 只显示前5个
            print(string.format("  %d: 0x%X", i, xResults.addrs[i]))
        end
    end
    
    if yResults.count > 0 and yResults.count <= 20 then
        print("Y坐标可能的地址:")
        for i = 1, math.min(yResults.count, 5) do  -- 只显示前5个
            print(string.format("  %d: 0x%X", i, yResults.addrs[i]))
        end
    end
    
    return xResults, yResults
end

-- 获取当前坐标（如果知道地址的话）
function getCurrentCoordinates()
    -- 这个函数需要先通过teleport()函数运行一次来获取地址
    -- 或者通过searchCoordinates()找到地址后手动设置
    print("此功能需要先运行瞬移功能来获取坐标地址")
end

-- 中文函数别名
function 瞬移(x坐标, y坐标)
    return teleport(x坐标, y坐标)
end

function 搜索坐标(当前x, 当前y)
    return searchCoordinates(当前x, 当前y)
end

-- 显示插件信息
function showInfo()
    print("=== BigWhiteRead插件信息 ===")
    print("目标包名: " .. packageName)
    print("进程PID: " .. Mem.Pid)
    
    -- 显示支持的架构
    local cpuArch = getCpuArch()
    local archNames = {
        [0] = "x86 (32位)",
        [1] = "ARM (32位)", 
        [2] = "ARM64 (64位)",
        [3] = "x86_64 (64位)"
    }
    print("当前架构: " .. (archNames[cpuArch] or "未知"))
    
    -- 测试模块获取
    local moduleBase = Mem.GetModuleBase("libgame.so")
    if moduleBase ~= 0 then
        print(string.format("libgame.so基址: 0x%X", moduleBase))
        print("✅ 模块访问正常")
    else
        print("❌ 无法获取libgame.so模块")
    end
end

-- 使用说明
print("=== BigWhiteRead瞬移脚本使用说明 ===")
print("功能函数:")
print("  teleport(x, y)           - 瞬移到指定坐标")
print("  searchCoordinates(x, y)  - 搜索坐标地址（备用）")
print("  showInfo()               - 显示插件信息")
print("")
print("中文函数:")
print("  瞬移(x坐标, y坐标)")
print("  搜索坐标(当前x, 当前y)")
print("")
print("优势:")
print("  ✅ 支持32位和64位系统")
print("  ✅ 不需要搜索，直接计算地址")
print("  ✅ API简洁强大")
print("  ✅ 自动架构适配")
print("")

-- 显示插件信息
showInfo()

-- 测试瞬移
print("开始测试瞬移...")
teleport(80, 50)
