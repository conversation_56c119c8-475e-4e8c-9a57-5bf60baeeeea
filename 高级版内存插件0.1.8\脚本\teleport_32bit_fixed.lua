-- 针对32位进程优化的瞬移功能
-- 使用高级版内存插件0.1.8，兼容32位和64位进程

local plugin = require("mem")
local mem = plugin:new("mem.apk")

-- 目标应用包名
local pkgname = "ccz.locojoy.mini.mt34.joysdk"

-- 获取目标进程PID
print("正在获取目标进程PID...")
mem:get_pid(pkgname)
print("✅ 成功连接到目标进程: " .. pkgname)

-- 兼容32位和64位的瞬移功能
function teleport_compatible(x, y)
    print("=== 兼容性瞬移功能 ===")
    print(string.format("目标坐标: X=%.2f, Y=%.2f", x, y))
    
    -- 1. 检查进程架构
    local arch = mem:getProcessArchitecture()
    print(string.format("进程架构: %d位", arch))
    
    -- 2. 获取libgame.so模块信息
    local so_info = mem:getModuleInfo("libgame.so")
    if not so_info or #so_info == 0 then
        print("❌ 获取libgame.so模块信息失败")
        return false
    end
    
    print(string.format("✅ 获取到libgame.so模块信息，共%d个内存段", #so_info))
    local module_base = so_info[1].start
    print(string.format("模块基址: 0x%X", module_base))
    
    -- 3. 计算BSS段地址
    local bss_offset = 0xD37684
    local bss_addr = module_base + bss_offset
    print(string.format("BSS段地址: 0x%X", bss_addr))
    
    -- 4. 读取BSS地址的值
    local read_bss_value, err = mem:read_memory(bss_addr, mem.ui32)
    if not read_bss_value then
        print("❌ 读取BSS地址失败: " .. (err or "未知错误"))
        return false
    end
    
    -- 5. 确定起始地址
    local chain_start = read_bss_value ~= 0 and read_bss_value or bss_addr
    print(string.format("起始地址: 0x%X", chain_start))
    
    -- 6. 根据架构选择偏移计算方法
    local final_address
    
    if arch == 64 then
        print("🚀 使用64位锁链偏移功能")
        -- 使用0.1.8版本的锁链偏移功能
        final_address = mem:fuckChain(chain_start, "0x78+0x11C")
        if not final_address or final_address == 0 then
            print("❌ 锁链偏移计算失败，回退到传统方法")
            -- 回退到传统方法
            local offset1, _ = mem:read_memory(chain_start + 0x78, mem.ui32)
            if not offset1 then return false end
            local offset2, _ = mem:read_memory(offset1 + 0x11C, mem.ui32)
            if not offset2 then return false end
            final_address = offset2
        end
    else
        print("⚙️ 使用32位传统偏移方法")
        -- 32位进程使用传统的逐级读取
        local offset1, err1 = mem:read_memory(chain_start + 0x78, mem.ui32)
        if not offset1 then
            print("❌ 读取第一级偏移失败: " .. (err1 or "未知错误"))
            return false
        end
        print(string.format("第一级偏移: 0x%X", offset1))
        
        local offset2, err2 = mem:read_memory(offset1 + 0x11C, mem.ui32)
        if not offset2 then
            print("❌ 读取第二级偏移失败: " .. (err2 or "未知错误"))
            return false
        end
        print(string.format("第二级偏移: 0x%X", offset2))
        
        final_address = offset2
    end
    
    print(string.format("✅ 最终地址计算成功: 0x%X", final_address))
    
    -- 7. 计算坐标地址
    local x_address = final_address + 0x20 - 20  -- X坐标地址
    local y_address = final_address + 0x20 - 16  -- Y坐标地址
    
    print(string.format("X坐标地址: 0x%X", x_address))
    print(string.format("Y坐标地址: 0x%X", y_address))
    
    -- 8. 地址有效性检查
    if x_address <= 0 or y_address <= 0 then
        print("❌ 计算出的地址无效")
        return false
    end
    
    -- 9. 修改坐标值
    local x_coord = x * 24
    local y_coord = y * 16
    
    print(string.format("正在修改坐标: X=%.2f, Y=%.2f", x_coord, y_coord))
    
    -- 修改X坐标
    local x_result = mem:write_memory(x_address, x_coord, mem.f32)
    if not x_result then
        print("❌ 修改X坐标失败")
        return false
    end
    
    -- 修改Y坐标
    local y_result = mem:write_memory(y_address, y_coord, mem.f32)
    if not y_result then
        print("❌ 修改Y坐标失败")
        return false
    end
    
    print("✅ 坐标修改成功")
    
    -- 10. 验证修改结果
    local verify_x, _ = mem:read_memory(x_address, mem.f32)
    local verify_y, _ = mem:read_memory(y_address, mem.f32)
    
    if verify_x and verify_y then
        print(string.format("✅ 验证成功 - 实际坐标 X: %.2f, Y: %.2f", verify_x, verify_y))
    else
        print("⚠️ 无法验证修改结果，但修改操作已执行")
    end
    
    print("✅ 瞬移坐标设置完成！")
    
    -- 11. 执行点击操作
    print("执行后续操作...")
    if _G.sleep then _G.sleep(1500) else print("延时1500ms") end
    if _G.tap then _G.tap(648, 362) else print("点击(648, 362)") end
    if _G.sleep then _G.sleep(500) else print("延时500ms") end
    
    return true
end

-- 显示插件功能支持情况
function show_plugin_capabilities()
    print("=== 插件功能支持情况 ===")
    local arch = mem:getProcessArchitecture()
    
    print(string.format("进程架构: %d位", arch))
    
    if arch == 64 then
        print("✅ 支持的功能:")
        print("  • 锁链偏移计算 (fuckChain)")
        print("  • 内存权限管理")
        print("  • SO文件注入")
        print("  • 模块信息获取")
        print("  • 模糊PID搜索")
    else
        print("✅ 支持的功能:")
        print("  • 传统偏移计算")
        print("  • 模块信息获取")
        print("  • 模糊PID搜索")
        print("❌ 不支持的功能:")
        print("  • 内存权限管理")
        print("  • SO文件注入")
        print("⚠️ 锁链偏移功能可能不稳定")
    end
end

-- 主程序
print("=== 高级版内存插件0.1.8 - 兼容性瞬移演示 ===")
show_plugin_capabilities()

-- 执行瞬移测试
print("\n开始瞬移测试...")
local success = teleport_compatible(50, 50)

if success then
    print("🎉 瞬移操作完成！")
else
    print("💥 瞬移操作失败！")
end

print("\n=== 演示结束 ===")
