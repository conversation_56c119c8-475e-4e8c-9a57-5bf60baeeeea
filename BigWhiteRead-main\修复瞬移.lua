-- 修复版瞬移脚本 - 使用重写后的智能ReadPointer
require("read")

-- 配置参数（提取硬编码）
local CONFIG = {
    packageName = "ccz.locojoy.mini.mt34.joysdk",  -- 游戏包名
    moduleName = "libgame.so",                     -- 游戏模块名
    baseOffset = 0xD37684,                         -- BSS偏移
    pointerOffsets = {0x78, 0x11C},               -- 指针链偏移
    coordOffsets = {x = 0x20 - 20, y = 0x20 - 16}, -- 坐标偏移
    coordMultipliers = {x = 24, y = 16}            -- 坐标倍数
}

print("使用智能架构适配的ReadPointer函数")

-- 连接到游戏进程
Mem.GetPID(CONFIG.packageName)
if Mem.Pid == 0 then
    print("未找到目标进程: " .. CONFIG.packageName)
    return
end

print("✅ 连接成功，PID: " .. Mem.Pid)

-- 瞬移函数（使用配置参数）
function 瞬移(x, y)
    -- 参数验证
    if type(x) ~= "number" or type(y) ~= "number" then
        print("❌ 坐标参数必须是数字")
        return false
    end

    print("开始瞬移...")

    -- 获取游戏主模块基址
    local baseAddr = Mem.GetModuleBase(CONFIG.moduleName)
    if baseAddr == 0 then
        print("未找到游戏模块: " .. CONFIG.moduleName)
        return false
    end
    print("模块基址:", string.format("0x%X", baseAddr))

    -- 使用修复后的ReadPointer函数
    print("开始多级指针读取...")
    local targetAddr = ReadPointer(baseAddr + CONFIG.baseOffset, table.unpack(CONFIG.pointerOffsets))

    if not targetAddr or targetAddr == 0 then
        print("❌ ReadPointer失败")
        return false
    end

    print("✅ 目标地址:", string.format("0x%X", targetAddr))

    -- 计算坐标值
    local xValue = x * CONFIG.coordMultipliers.x
    local yValue = y * CONFIG.coordMultipliers.y

    -- 修改坐标值
    Mem.WriteFloat(targetAddr + CONFIG.coordOffsets.x, xValue)
    Mem.WriteFloat(targetAddr + CONFIG.coordOffsets.y, yValue)

    print(string.format("✅ 瞬移成功: (%.0f, %.0f)", xValue, yValue))
    return true
end


-- 测试瞬移
瞬移(90, 50)
