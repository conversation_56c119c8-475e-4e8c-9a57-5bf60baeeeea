0.1.8
新增so文件注入
新增运行进程的位数获取
////////////////////////////
0.1.7
移除搜索
移除32位
优化模块信息和模糊匹配的速度
新增内存权限的相关函数
////////////////////////////
0.1.6
新增锁链偏移
////////////////////////////
0.1.5
跟随懒人1492更新
////////////////////////////
0.1.4
新增apk插件
new 函数可以加载apk插件
////////////////////////////
0.1.3
修改了一下解析范围，模仿了一下gg修改器，不过仍有一些出入，问题不大
////////////////////////////
0.1.2
联合搜索新增nop键，用于过滤部分无效内存页（简而言之,有一定过搜检的效果）但是搜索速度会慢,且 你有过墙梯，人有张良计
/////////////////////////////
0.1.1
修复get_pid的一个bug
提升联合搜索一点点速度(忽略不计)
/////////////////////////////
0.1.0
新增函数get_pidX(package_name) 模糊搜索pid 支持 * ? 两种通配符
/////////////////////////////
0.0.9
联合搜索增加移除指定区域
Remove.Star Remove.End
修复 自定义区域(rangs)和解析区域(types) 可以同时生效
修复 0.0.7 遗留的的一个bug
//////////////////////////////
0.0.8
联合搜索增加自定义区域设置
rangs.Star rangs.End
//////////////////////////////
0.0.7
优化模式1的效率
新增ConvertAbi 转换已安装app的api
新增getAbi 获取已安装app的api
//////////////////////////////
0.0.6
添加set_mode(int)
用户设置读写搜的模式
0：syscall  
1：proc 支持超长地址
/////////////////////////////
0.0.5
修复两个小bug
//////////////////////////////
0.0.4
新增字符串读写
搜索增加字符串（不得较长的字符串，字符串搜索不支持模糊搜索）
//////////////////////////////