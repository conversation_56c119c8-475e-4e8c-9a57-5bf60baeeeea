-- BigWhiteRead瞬移模块 - 中文版
-- 封装瞬移功能，使用中文变量和函数名

require("read")

-- 瞬移模块
local 瞬移模块 = {}

-- 配置
local 目标包名 = "ccz.locojoy.mini.mt34.joysdk"
local 已连接 = false
local 进程ID = 0

-- 初始化连接
local function 初始化连接()
    if not 已连接 then
        进程ID = Mem.GetPID(目标包名)
        if 进程ID == 0 then
            print("❌ 连接失败，请确认游戏正在运行")
            return false
        end
        已连接 = true
        print("✅ 连接成功，PID: " .. 进程ID)
    end
    return true
end

-- 核心瞬移函数
function 瞬移模块.瞬移到(x坐标, y坐标)
    if not 初始化连接() then
        return false
    end

    -- 获取游戏模块基址
    local 模块基址 = Mem.GetModuleBase("libgame.so")
    if 模块基址 == 0 then
        return false
    end

    -- 计算BSS地址
    local bss地址 = 模块基址 + 0xD37684
    local bss值 = Mem.GetDword(bss地址)
    local 基础地址 = (bss值 ~= 0 and bss值 < 0x80000000) and bss值 or bss地址

    -- 多级指针偏移
    local 偏移1 = Mem.GetDword(基础地址 + 0x78)
    if not 偏移1 or 偏移1 == 0 then return false end

    local 偏移2 = Mem.GetDword(偏移1 + 0x11C)
    if not 偏移2 or 偏移2 == 0 then return false end

    -- 计算坐标地址并修改
    local x地址 = 偏移2 + 0x20 - 20
    local y地址 = 偏移2 + 0x20 - 16
    local x实际值 = x坐标 * 24
    local y实际值 = y坐标 * 16

    Mem.WriteFloat(x地址, x实际值)
    Mem.WriteFloat(y地址, y实际值)

    print(string.format("✅ 瞬移成功: (%.0f, %.0f)", x实际值, y实际值))
    return true
end

-- 简化调用函数
function 瞬移模块.瞬移(x, y)
    return 瞬移模块.瞬移到(x, y)
end

-- 获取当前坐标
function 瞬移模块.获取坐标()
    if not 初始化连接() then
        return nil, nil
    end

    local 模块基址 = Mem.GetModuleBase("libgame.so")
    if 模块基址 == 0 then return nil, nil end

    local bss地址 = 模块基址 + 0xD37684
    local bss值 = Mem.GetDword(bss地址)
    local 基础地址 = (bss值 ~= 0 and bss值 < 0x80000000) and bss值 or bss地址

    local 偏移1 = Mem.GetDword(基础地址 + 0x78)
    if not 偏移1 or 偏移1 == 0 then return nil, nil end

    local 偏移2 = Mem.GetDword(偏移1 + 0x11C)
    if not 偏移2 or 偏移2 == 0 then return nil, nil end

    local x地址 = 偏移2 + 0x20 - 20
    local y地址 = 偏移2 + 0x20 - 16

    local x值 = Mem.GetFloat(x地址)
    local y值 = Mem.GetFloat(y地址)

    if x值 and y值 then
        return x值 / 24, y值 / 16
    end

    return nil, nil
end

-- 批量瞬移
function 瞬移模块.批量瞬移(坐标列表, 间隔时间)
    间隔时间 = 间隔时间 or 1000
    local 成功次数 = 0

    for i, 坐标 in ipairs(坐标列表) do
        if 瞬移模块.瞬移(坐标[1], 坐标[2]) then
            成功次数 = 成功次数 + 1
        end

        if i < #坐标列表 and _G.sleep then
            _G.sleep(间隔时间)
        end
    end

    print(string.format("批量瞬移完成: %d/%d", 成功次数, #坐标列表))
    return 成功次数
end

-- 导出全局函数，方便直接调用
_G.瞬移 = 瞬移模块.瞬移
_G.瞬移到 = 瞬移模块.瞬移到
_G.获取坐标 = 瞬移模块.获取坐标
_G.批量瞬移 = 瞬移模块.批量瞬移

-- 英文别名（兼容性）
_G.teleport = 瞬移模块.瞬移
_G.tp = 瞬移模块.瞬移

-- 使用说明
print("=== BigWhiteRead瞬移模块 - 中文版 ===")
print("可用函数:")
print("  瞬移(x, y)           - 瞬移到指定坐标")
print("  瞬移到(x, y)         - 同上")
print("  获取坐标()           - 获取当前坐标")
print("  批量瞬移(坐标列表)    - 批量瞬移")
print("")
print("使用示例:")
print("  瞬移(50, 50)")
print("  local x, y = 获取坐标()")
print("  批量瞬移({{10,20}, {30,40}})")

return 瞬移模块
