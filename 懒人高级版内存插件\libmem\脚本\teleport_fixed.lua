-- 懒人版内存插件瞬移脚本 - 修复版
-- 使用懒人版插件的正确API实现瞬移功能

-- 导入懒人版内存插件
local MemsyTool = require("mem")

-- 配置
local packageName = "ccz.locojoy.mini.mt34.joysdk"
local memTool = nil

-- 坐标地址缓存（找到后保存，避免重复搜索）
local xCoordAddress = nil
local yCoordAddress = nil

-- 初始化内存工具
local function initTool()
    if not memTool then
        print("正在连接到进程: " .. packageName)
        memTool = MemsyTool.new(packageName)
        local pid, err = memTool:getPID()
        if not pid then
            print("❌ 连接失败: " .. (err or "未知错误"))
            print("请确认游戏是否正在运行")
            return nil
        end
        print("✅ 成功连接，PID: " .. pid)
    end
    return memTool
end

-- 方法1：通过搜索找到坐标地址
function findCoordinateAddresses(currentX, currentY)
    print("=== 搜索坐标地址 ===")
    print(string.format("当前坐标: X=%.1f, Y=%.1f", currentX, currentY))
    
    local tool = initTool()
    if not tool then
        return false
    end
    
    -- 计算内存中的实际值
    local xMemValue = currentX * 24
    local yMemValue = currentY * 16
    
    print(string.format("搜索内存值: X=%.0f, Y=%.0f", xMemValue, yMemValue))
    
    -- 搜索X坐标
    print("搜索X坐标...")
    local xResults = tool:search(xMemValue, 1, 0x0, 0x7FFFFFFF)  -- 1 = FLOAT类型
    if xResults then
        local xCount = MemsyTool.getResultCount(xResults)
        print(string.format("X坐标搜索结果: %d个", xCount))
        
        if xCount > 0 and xCount <= 50 then  -- 合理的结果数量
            local xAddresses = MemsyTool.getResultAddresses(xResults)
            if #xAddresses > 0 then
                xCoordAddress = xAddresses[1]
                print(string.format("✅ X坐标地址: 0x%X", xCoordAddress))
            end
        else
            print("⚠️ X坐标搜索结果过多或为空，请尝试更特殊的坐标")
        end
    end
    
    -- 搜索Y坐标
    print("搜索Y坐标...")
    local yResults = tool:search(yMemValue, 1, 0x0, 0x7FFFFFFF)  -- 1 = FLOAT类型
    if yResults then
        local yCount = MemsyTool.getResultCount(yResults)
        print(string.format("Y坐标搜索结果: %d个", yCount))
        
        if yCount > 0 and yCount <= 50 then  -- 合理的结果数量
            local yAddresses = MemsyTool.getResultAddresses(yResults)
            if #yAddresses > 0 then
                yCoordAddress = yAddresses[1]
                print(string.format("✅ Y坐标地址: 0x%X", yCoordAddress))
            end
        else
            print("⚠️ Y坐标搜索结果过多或为空，请尝试更特殊的坐标")
        end
    end
    
    if xCoordAddress and yCoordAddress then
        print("🎉 坐标地址搜索成功！")
        return true
    else
        print("❌ 坐标地址搜索失败")
        print("建议：")
        print("1. 移动到一个特殊的坐标位置（如123.5, 456.7）")
        print("2. 确保坐标值是准确的")
        print("3. 重新运行搜索")
        return false
    end
end

-- 方法2：验证找到的地址
function verifyAddresses()
    if not xCoordAddress or not yCoordAddress then
        print("❌ 没有找到坐标地址，请先运行 findCoordinateAddresses()")
        return false
    end
    
    local tool = initTool()
    if not tool then
        return false
    end
    
    print("=== 验证坐标地址 ===")
    
    -- 读取当前坐标值
    local xValue = tool:read(xCoordAddress, 1)  -- 1 = FLOAT类型
    local yValue = tool:read(yCoordAddress, 1)
    
    if xValue and yValue then
        local gameX = xValue / 24
        local gameY = yValue / 16
        print(string.format("✅ 当前游戏坐标: X=%.1f, Y=%.1f", gameX, gameY))
        print(string.format("内存值: X=%.0f, Y=%.0f", xValue, yValue))
        return true
    else
        print("❌ 无法读取坐标值，地址可能不正确")
        return false
    end
end

-- 方法3：使用找到的地址进行瞬移
function teleportWithAddresses(targetX, targetY)
    if not xCoordAddress or not yCoordAddress then
        print("❌ 没有坐标地址，请先运行 findCoordinateAddresses()")
        return false
    end
    
    print(string.format("=== 瞬移到坐标 (%.1f, %.1f) ===", targetX, targetY))
    
    local tool = initTool()
    if not tool then
        return false
    end
    
    -- 计算目标坐标的内存值
    local xMemValue = targetX * 24
    local yMemValue = targetY * 16
    
    print(string.format("写入内存值: X=%.0f, Y=%.0f", xMemValue, yMemValue))
    
    -- 写入坐标
    local xResult = tool:write(xCoordAddress, xMemValue, 1)  -- 1 = FLOAT类型
    local yResult = tool:write(yCoordAddress, yMemValue, 1)
    
    if xResult and yResult then
        print("✅ 坐标修改成功！")
        
        -- 验证修改结果
        local newX = tool:read(xCoordAddress, 1)
        local newY = tool:read(yCoordAddress, 1)
        if newX and newY then
            print(string.format("验证结果: X=%.1f, Y=%.1f", newX/24, newY/16))
        end
        
        return true
    else
        print("❌ 坐标修改失败")
        return false
    end
end

-- 主瞬移函数
function teleport(x, y)
    if not xCoordAddress or not yCoordAddress then
        print("❌ 尚未找到坐标地址")
        print("请按以下步骤操作：")
        print("1. 移动到一个已知坐标位置")
        print("2. 运行: findCoordinateAddresses(当前X, 当前Y)")
        print("3. 运行: teleport(目标X, 目标Y)")
        return false
    end
    
    return teleportWithAddresses(x, y)
end

-- 中文函数别名
function 搜索坐标地址(当前x, 当前y)
    return findCoordinateAddresses(当前x, 当前y)
end

function 验证地址()
    return verifyAddresses()
end

function 瞬移(目标x, 目标y)
    return teleport(目标x, 目标y)
end

-- 显示当前状态
function showStatus()
    print("=== 懒人版瞬移脚本状态 ===")
    print("目标包名: " .. packageName)
    
    if memTool and memTool.pid then
        print("进程连接: ✅ PID " .. memTool.pid)
    else
        print("进程连接: ❌ 未连接")
    end
    
    if xCoordAddress then
        print(string.format("X坐标地址: ✅ 0x%X", xCoordAddress))
    else
        print("X坐标地址: ❌ 未找到")
    end
    
    if yCoordAddress then
        print(string.format("Y坐标地址: ✅ 0x%X", yCoordAddress))
    else
        print("Y坐标地址: ❌ 未找到")
    end
    
    if xCoordAddress and yCoordAddress then
        print("状态: ✅ 可以使用瞬移功能")
        verifyAddresses()
    else
        print("状态: ⚠️ 需要先搜索坐标地址")
    end
end

-- 使用说明
print("=== 懒人版内存插件瞬移脚本 ===")
print("支持32位系统，使用搜索方式定位坐标")
print("")
print("使用步骤：")
print("1. showStatus()                    - 查看当前状态")
print("2. findCoordinateAddresses(x, y)   - 搜索坐标地址")
print("3. verifyAddresses()               - 验证地址正确性")
print("4. teleport(newX, newY)            - 瞬移到新坐标")
print("")
print("中文函数：")
print("1. 搜索坐标地址(当前x, 当前y)")
print("2. 验证地址()")
print("3. 瞬移(目标x, 目标y)")
print("")

-- 显示初始状态
showStatus()
