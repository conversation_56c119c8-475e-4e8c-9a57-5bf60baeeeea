-- 超简化瞬移脚本 - 日常使用版
-- 一键瞬移，自动处理所有细节

local plugin = require("mem")
local mem = plugin:new("mem.apk")

-- 配置
local PKG = "ccz.locojoy.mini.mt34.joysdk"
local initialized = false

-- 初始化
local function init()
    if not initialized then
        mem:get_pid(PKG)
        initialized = true
    end
end

-- 核心瞬移函数
function 瞬移(x, y)
    init()
    
    -- 获取地址
    local so_info = mem:getModuleInfo("libgame.so")
    local base = so_info[1].start
    local bss = base + 0xD37684
    local read_bss = mem:read_memory(bss, mem.ui32)
    local start = read_bss ~= 0 and read_bss or bss
    
    -- 计算最终地址
    local addr
    local arch = mem:getProcessArchitecture()
    
    if arch == 64 then
        -- 64位：尝试锁链偏移
        addr = mem:fuck<PERSON>hain(start, "0x78+0x11C")
        if not addr or addr == 0 then
            -- 回退到传统方法
            local o1 = mem:read_memory(start + 0x78, mem.ui32)
            local o2 = mem:read_memory(o1 + 0x11C, mem.ui32)
            addr = o2
        end
    else
        -- 32位：传统方法
        local o1 = mem:read_memory(start + 0x78, mem.ui32)
        local o2 = mem:read_memory(o1 + 0x11C, mem.ui32)
        addr = o2
    end
    
    -- 修改坐标
    mem:write_memory(addr + 0x20 - 20, x * 24, mem.f32)
    mem:write_memory(addr + 0x20 - 16, y * 16, mem.f32)
    
  
    
    print(string.format("✅ 瞬移到: (%.0f, %.0f)", x * 24, y * 16))
    return true
end


瞬移(50, 50)
