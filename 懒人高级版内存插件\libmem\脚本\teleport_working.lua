-- Working Teleport Script using MemsyTool
-- Step-by-step coordinate search and teleport

local MemsyTool = require("mem")

-- Configuration
local packageName = "ccz.locojoy.mini.mt34.joysdk"
local memTool = nil

-- Data type constants (matching MemsyTool)
local DWORD = 0   -- Integer
local FLOAT = 1   -- Float

-- Global variables to store found addresses
local xCoordAddress = nil
local yCoordAddress = nil

-- Initialize memory tool
local function initMemTool()
    if not memTool then
        print("Connecting to process: " .. packageName)
        memTool = MemsyTool.new(packageName)
        local pid, err = memTool:getPID()
        if not pid then
            print("Connection failed: " .. (err or "Unknown error"))
            return nil
        end
        print("Successfully connected to process, PID: " .. pid)
    end
    return memTool
end

-- Step 1: Search for coordinate values
function searchCoordinates(xValue, yValue)
    print("=== Searching for Coordinates ===")
    print(string.format("Searching for X=%.1f, Y=%.1f", xValue, yValue))
    
    local tool = initMemTool()
    if not tool then
        return false
    end
    
    -- Calculate the actual values stored in memory
    local xMemValue = xValue * 24
    local yMemValue = yValue * 16
    
    print(string.format("Searching for memory values: X=%.0f, Y=%.0f", xMemValue, yMemValue))
    
    -- Search for X coordinate
    print("Searching for X coordinate...")
    local xResults = tool:search(xMemValue, FLOAT, 0x0, 0x7FFFFFFF)
    if xResults then
        local xCount = MemsyTool.getResultCount(xResults)
        print(string.format("Found %d results for X coordinate", xCount))
        
        if xCount > 0 and xCount < 100 then  -- Reasonable number of results
            local xAddresses = MemsyTool.getResultAddresses(xResults)
            print("X coordinate addresses found:")
            for i, addr in ipairs(xAddresses) do
                print(string.format("  %d: 0x%X", i, addr))
            end
            
            -- Use the first address as a candidate
            if #xAddresses > 0 then
                xCoordAddress = xAddresses[1]
                print(string.format("Using X address: 0x%X", xCoordAddress))
            end
        end
    end
    
    -- Search for Y coordinate
    print("Searching for Y coordinate...")
    local yResults = tool:search(yMemValue, FLOAT, 0x0, 0x7FFFFFFF)
    if yResults then
        local yCount = MemsyTool.getResultCount(yResults)
        print(string.format("Found %d results for Y coordinate", yCount))
        
        if yCount > 0 and yCount < 100 then  -- Reasonable number of results
            local yAddresses = MemsyTool.getResultAddresses(yResults)
            print("Y coordinate addresses found:")
            for i, addr in ipairs(yAddresses) do
                print(string.format("  %d: 0x%X", i, addr))
            end
            
            -- Use the first address as a candidate
            if #yAddresses > 0 then
                yCoordAddress = yAddresses[1]
                print(string.format("Using Y address: 0x%X", yCoordAddress))
            end
        end
    end
    
    if xCoordAddress and yCoordAddress then
        print("✅ Coordinate addresses found successfully!")
        return true
    else
        print("❌ Could not find coordinate addresses")
        print("Try moving to a different position and search again")
        return false
    end
end

-- Step 2: Verify found addresses by reading current values
function verifyAddresses()
    if not xCoordAddress or not yCoordAddress then
        print("❌ No addresses found. Run searchCoordinates() first.")
        return false
    end
    
    local tool = initMemTool()
    if not tool then
        return false
    end
    
    print("=== Verifying Addresses ===")
    
    local xValue = tool:read(xCoordAddress, FLOAT)
    local yValue = tool:read(yCoordAddress, FLOAT)
    
    if xValue and yValue then
        print(string.format("Current coordinates: X=%.1f, Y=%.1f", xValue/24, yValue/16))
        print(string.format("Memory values: X=%.0f, Y=%.0f", xValue, yValue))
        return true
    else
        print("❌ Failed to read coordinate values")
        return false
    end
end

-- Step 3: Teleport using found addresses
function teleportToCoords(x, y)
    if not xCoordAddress or not yCoordAddress then
        print("❌ No addresses found. Run searchCoordinates() first.")
        return false
    end
    
    print(string.format("=== Teleporting to (%.1f, %.1f) ===", x, y))
    
    local tool = initMemTool()
    if not tool then
        return false
    end
    
    -- Calculate memory values
    local xMemValue = x * 24
    local yMemValue = y * 16
    
    print(string.format("Writing values: X=%.0f to 0x%X, Y=%.0f to 0x%X", 
          xMemValue, xCoordAddress, yMemValue, yCoordAddress))
    
    -- Write coordinates
    local xResult = tool:write(xCoordAddress, xMemValue, FLOAT)
    local yResult = tool:write(yCoordAddress, yMemValue, FLOAT)
    
    if xResult and yResult then
        print("✅ Coordinates written successfully!")
        
        -- Verify the write
        local newX = tool:read(xCoordAddress, FLOAT)
        local newY = tool:read(yCoordAddress, FLOAT)
        
        if newX and newY then
            print(string.format("Verification: X=%.1f, Y=%.1f", newX/24, newY/16))
        end
        
        return true
    else
        print("❌ Failed to write coordinates")
        return false
    end
end

-- Complete teleport process
function teleport(x, y)
    print("=== Complete Teleport Process ===")
    
    if not xCoordAddress or not yCoordAddress then
        print("No coordinate addresses found.")
        print("Please follow these steps:")
        print("1. Move your character to a known position")
        print("2. Run: searchCoordinates(currentX, currentY)")
        print("3. Then run: teleport(newX, newY)")
        return false
    end
    
    return teleportToCoords(x, y)
end

-- Helper function to show current status
function showStatus()
    print("=== Teleport Script Status ===")
    print("Package: " .. packageName)
    
    if memTool then
        local pid = memTool.pid
        print("Connected to PID: " .. (pid or "Unknown"))
    else
        print("Not connected to process")
    end
    
    if xCoordAddress then
        print(string.format("X coordinate address: 0x%X", xCoordAddress))
    else
        print("X coordinate address: Not found")
    end
    
    if yCoordAddress then
        print(string.format("Y coordinate address: 0x%X", yCoordAddress))
    else
        print("Y coordinate address: Not found")
    end
    
    if xCoordAddress and yCoordAddress then
        verifyAddresses()
    end
end

-- Usage instructions
print("=== Working Teleport Script ===")
print("Step-by-step usage:")
print("1. showStatus()                    - Check current status")
print("2. searchCoordinates(x, y)         - Search for coordinate addresses")
print("3. verifyAddresses()               - Verify found addresses")
print("4. teleport(newX, newY)            - Teleport to new coordinates")
print("")
print("Example:")
print("1. Move to position (10, 20) in game")
print("2. Run: searchCoordinates(10, 20)")
print("3. Run: teleport(80, 50)")
print("")

-- Show initial status
showStatus()
