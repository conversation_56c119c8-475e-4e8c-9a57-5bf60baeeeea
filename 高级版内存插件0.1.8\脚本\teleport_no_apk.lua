-- 不依赖APK的瞬移脚本
-- 直接使用SO文件路径

-- 尝试直接加载SO文件
local plugin = require("mem")
local mem = nil

-- 配置
local packageName = "ccz.locojoy.mini.mt34.joysdk"

-- 尝试不同的SO文件路径
local function tryLoadMem()
    local soPaths = {
        "/data/local/tmp/lrwork/com.nx.nxprojit/files/pluginapk/43b59731407c01091946bb495a2b6e56/lib/arm64-v8a/libmem.so",
        "/data/local/tmp/lrwork/com.nx.nxprojit/files/pluginapk/43b59731407c01091946bb495a2b6e56/lib/x86_64/libmem.so",
        "/data/data/com.nx.nxprojit/files/libmem.so",
        "./libmem.so"
    }
    
    for i, path in ipairs(soPaths) do
        print(string.format("尝试加载SO文件 %d: %s", i, path))
        
        local success, result = pcall(function()
            return plugin:new(path)
        end)
        
        if success and result then
            print("✅ SO文件加载成功: " .. path)
            return result
        else
            print("❌ 加载失败: " .. (result or "未知错误"))
        end
    end
    
    return nil
end

-- 初始化内存插件
print("=== 初始化内存插件 ===")
mem = tryLoadMem()

if not mem then
    print("❌ 所有SO文件加载失败")
    print("请检查SO文件是否存在，或使用其他方法")
    return
end

-- 连接到进程
print("连接到进程: " .. packageName)
local success, err = pcall(function()
    mem:get_pid(packageName)
end)

if not success then
    print("❌ 连接进程失败: " .. (err or "未知错误"))
    return
end

print("✅ 成功连接到进程")

-- 瞬移函数
function teleport(x, y)
    print(string.format("开始瞬移到坐标: (%.1f, %.1f)", x, y))

    -- 获取libgame.so模块信息
    local moduleInfo = mem:getModuleInfo("libgame.so")
    if not moduleInfo or #moduleInfo == 0 then
        print("❌ 获取libgame.so模块失败")
        return false
    end

    local moduleBase = moduleInfo[1].start
    print(string.format("模块基址: 0x%X", moduleBase))

    -- 计算BSS地址
    local bssAddress = moduleBase + 0xD37684

    -- 读取BSS地址的值
    local bssValue, err = mem:read_memory(bssAddress, mem.ui32)
    if not bssValue then
        print("❌ 读取BSS地址失败: " .. (err or "未知错误"))
        return false
    end

    -- 确定基地址
    local baseAddress = bssValue ~= 0 and bssValue or bssAddress
    print(string.format("基地址: 0x%X", baseAddress))

    -- 检查进程架构并选择方法
    local arch = mem:getProcessArchitecture()
    local finalAddress

    if arch == 64 then
        -- 64位：使用锁链偏移功能
        print("使用64位锁链偏移功能")
        finalAddress = mem:fuckChain(baseAddress, "0x78+0x11C")
        if not finalAddress or finalAddress == 0 then
            print("锁链偏移失败，使用传统方法")
            local offset1, _ = mem:read_memory(baseAddress + 0x78, mem.ui32)
            local offset2, _ = mem:read_memory(offset1 + 0x11C, mem.ui32)
            finalAddress = offset2
        end
    else
        -- 32位：使用传统方法
        print("使用32位传统偏移方法")
        local offset1, err1 = mem:read_memory(baseAddress + 0x78, mem.ui32)
        if not offset1 then
            print("❌ 读取第一级偏移失败: " .. (err1 or "未知错误"))
            return false
        end

        local offset2, err2 = mem:read_memory(offset1 + 0x11C, mem.ui32)
        if not offset2 then
            print("❌ 读取第二级偏移失败: " .. (err2 or "未知错误"))
            return false
        end

        finalAddress = offset2
    end

    print(string.format("最终地址: 0x%X", finalAddress))

    -- 计算实际坐标值
    local xValue = x * 24
    local yValue = y * 16

    -- 修改X坐标
    local xResult = mem:write_memory(finalAddress + 0x20 - 20, xValue, mem.f32)
    if not xResult then
        print("❌ 修改X坐标失败")
        return false
    end

    -- 修改Y坐标
    local yResult = mem:write_memory(finalAddress + 0x20 - 16, yValue, mem.f32)
    if not yResult then
        print("❌ 修改Y坐标失败")
        return false
    end

    print(string.format("✅ 瞬移成功: (%.0f, %.0f)", xValue, yValue))

    -- 执行点击操作
    if _G.sleep then _G.sleep(1500) end
    if _G.tap then _G.tap(648, 362) end
    if _G.sleep then _G.sleep(500) end

    return true
end

-- 中文函数别名
function 瞬移(x坐标, y坐标)
    return teleport(x坐标, y坐标)
end

-- 使用示例
print("=== 不依赖APK的瞬移脚本 ===")
print("直接使用SO文件路径加载")
print("使用方法: teleport(x, y) 或 瞬移(x坐标, y坐标)")

-- 测试瞬移
if mem then
    print("开始测试瞬移...")
    teleport(80, 50)
else
    print("请先解决SO文件加载问题")
end
