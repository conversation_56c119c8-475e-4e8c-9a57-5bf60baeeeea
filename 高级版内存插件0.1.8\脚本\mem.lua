ffi.cdef[[
typedef int pid_t;
int test(pid_t pid,unsigned long long address,const char *perm);
/**
 * @brief 进程信息结构体
 * 
 * 用于存储单个进程的基本信息，包括进程ID和命令行。
 */
typedef struct
{
    char *cmdline;  ///< 进程命令行字符串
    pid_t pid;      ///< 进程ID
} pid_info;

/**
 * @brief 进程查询结果结构体
 * 
 * 用于存储多个进程的查询结果，包含进程数量和信息数组。
 */
typedef struct
{
    int length;         ///< 进程数量
    pid_info *pid_list; ///< 进程信息数组指针
} pid_result;

/**
 * @brief 模块信息结构体
 * 
 * 用于存储单个模块的内存映射信息，包括起始地址、大小和权限。
 */
typedef struct
{
    unsigned long long start;  ///< 模块起始地址
    size_t size;              ///< 模块大小（字节）
    char *perms;              ///< 内存权限字符串（如"r-xp"）
    char *path;               ///< 模块路径
} module_info;

/**
 * @brief 模块查询结果结构体
 * 
 * 用于存储多个模块的查询结果，包含模块数量和信息数组。
 */
typedef struct
{
    int length;               ///< 模块数量
    module_info *module_list; ///< 模块信息数组指针
} module_result;
/**
 * @brief 内存操作函数结构体
 * 
 * 定义了所有内存操作函数的函数指针，用于FFI接口。
 * 这个结构体被Lua FFI使用来调用C++函数。
 */
struct mem
{
	
    bool (*isMemory)(unsigned long long, pid_t, size_t);           ///< 检查内存区域有效性
    bool (*readMemory)(pid_t, unsigned long long, void *, size_t); ///< 读取内存
    bool (*writeMemory)(pid_t, unsigned long long, const void *, size_t); ///< 写入内存
    int (*getPid)(const char *);                                   ///< 获取进程PID
    pid_result *(*getPidX)(const char *);                          ///< 模糊搜索进程PID
    module_result *(*getModuleInfo)(pid_t, const char *);         ///< 获取模块信息
    void (*free_pid_result)(pid_result *);                         ///< 释放PID结果内存
    void (*free_module_result)(module_result *);                   ///< 释放模块结果内存
    char* (*getMemoryPermission)(pid_t , unsigned long long );     ///< 获取内存权限
    int (*setMemoryPermission)(pid_t , unsigned long long , const char *); ///< 设置内存权限
    int (*getProcessArchitecture)(pid_t);                          ///< 获取进程位数
	bool (*injector)(pid_t, const char *);  						///< 注入so文件
};

mem * get_mem();
int getuid();
]]


local Memory = {}

Memory.__index = Memory

--定义类型
local typeMap = {
	i8 = "int8_t" ,
	i16 = "int16_t" ,
	i32 = "int32_t" ,
	i64 = "int64_t" ,
	ui8 = "uint8_t" ,
	ui16 = "uint16_t" ,
	ui32 = "uint32_t" ,
	ui64 = "uint64_t" ,
	char = "char" ,
	uchar = "unsigned char" ,
	
	f32 = "float" ,
	f64 = "double" ,
	str = "char*" -- Adding string type
}

local abi = {
	arm = "armeabi-v7a" ,
	arm64 = "arm64-v8a" ,
	x86 = "x86" ,
	x64 = "x86_64"
}

--构造方法 lib_path so文件路径或者apk路径/附件 要求有执行权限
function Memory:new(lib_path)
	if ffi.C.getuid() ~= 0 then
		--error("当前进程权限不足, 需求 root + 最高环境")
	end
	local self = setmetatable({} , Memory)
	
	-- Assign type mappings to self
	for k , v in pairs(typeMap) do
		self[k] = v
	end
	
	for k , v in pairs(abi) do
		self[k] = v
	end
	local soPath = ""
	self.lib = nil
	self.mem = nil
	self.arch = nil

	if lib_path:match("%.so$") then
		exec("chmod 777 "..lib_path)
		soPath = lib_path
		
	elseif lib_path:match("%.apk$") then
		import('com.nx.assist.lua.LuaEngine')
		local cpu = getCpuArch()
		if cpu == 0 or cpu == 1 then
			error("暂不支持32位")
		end
		local arch = {
			[2] = "arm64-v8a" ,
			[3] = "x86_64"
		}
		local loader = LuaEngine.loadApk(lib_path)
		soPath = loader.getApkPath():gsub("base%.apk$" , "lib/")..arch[getCpuArch()].."/libmem.so"
	else
		error("so ? apk ?")
	end
	self.lib = ffi.load(soPath)
	self.mem = self.lib.get_mem()
	self.pid = - 1
	return self
end

--获取包名pid 包名
function Memory:get_pid(package_name)
	self.pid = self.mem.getPid(package_name) or - 1
	self.arch = self.mem.getProcessArchitecture(self.pid)
	if self.pid == - 1 then error("pid is -1") end
	if self.arch ~= 64 then
		print([[目标进程非64位，以下函数禁用
		setMemoryPermission
		injector
		]])
	end
	return self , self.pid
end

--模糊搜索pid 支持 * ? 两种通配符
function Memory:get_pidX(package_name)
	local result = {}
	local pidX = self.mem.getPidX(package_name)
	if pidX.length == 0 then
		return result
	end
	for i = 0 , pidX.length - 1 do
		local tmp = {
			["pid"] = pidX.pid_list[i].pid ,
			["cmdline"] = ffi.string(pidX.pid_list[i].cmdline)
		}
		table.insert(result , tmp)
	end
	self.mem.free_pid_result(pidX)
	return result
end

--设置pid
function Memory:set_pid(pid)
	self.pid = pid
    self.arch = self.mem.getProcessArchitecture(self.pid)
    if self.arch ~= 64 then
		print([[目标进程非64位，以下函数禁用
		setMemoryPermission
		injector
		]])
	end
	return self
end

--读内存 地址 类型 如果搜字符串 需要大小，默认不写64
function Memory:read_memory(address , type , buffer_size)
	if self.pid == - 1 then error("pid is -1") end
	if type == "char*" then
		-- Reading a string
		local buffer_size = buffer_size or 64 -- Adjust buffer size as needed
		local buffer = ffi.new("char[?]" , buffer_size)
		local success = self.mem.readMemory(self.pid , address , buffer , buffer_size)
		if success then
			return ffi.string(buffer)
		else
			return nil , "Read memory failed"
		end
	else
		-- Reading other types
		local size = ffi.sizeof(type)
		local buffer = ffi.new("char[?]" , size)
		local success = self.mem.readMemory(self.pid , address , buffer , size)
		if success then
			return ffi.cast(type .. "*" , buffer)[0]
		else
			return nil , "Read memory failed"
		end
	end
end

--写内存 地址 值 类型
function Memory:write_memory(address , value , type)
	if self.pid == - 1 then error("pid is -1") end
	if type == "char*" then
		-- Writing a string
		local size = #value + 1 -- Including null terminator
		local buffer = ffi.new("char[?]" , size)
		ffi.copy(buffer , value)
		local success = self.mem.writeMemory(self.pid , address , buffer , size)
		if success then
			return true
		else
			return nil , "Write memory failed"
		end
	else
		-- Writing other types
		local size = ffi.sizeof(type)
		local buffer = ffi.new(type .. "[1]" , value)
		local success = self.mem.writeMemory(self.pid , address , buffer , size)
		if success then
			return true
		else
			return nil , "Write memory failed"
		end
	end
end

--获取so信息/地址  so文件名
function Memory:getModuleInfo(moduleName)
	if self.pid == - 1 then error("pid is -1") end
	local result = {}
	local ModuleInfo = self.mem.getModuleInfo(self.pid ,moduleName)
	if ModuleInfo.length == 0 then
		return {}
	end
	for i = 0 , ModuleInfo.length - 1 do
		local tmp = {
			["start"] = ModuleInfo.module_list[i].start,
			["size"] = ModuleInfo.module_list[i].size,
			["perms"] = ffi.string(ModuleInfo.module_list[i].perms),
			["path"] = ffi.string(ModuleInfo.module_list[i].path)
		}
		table.insert(result,tmp)
	end
	self.mem.free_module_result(ModuleInfo)
	return result
end

--转换已安装APKabi 包名 api
function Memory:ConvertAbi(package , abi)
	local pmPath = exec("pm path "..package):match("package:(/.+)$"):gsub("\n" , "")
	local cmd = string.format("pm install -r --abi %s %s" , abi , pmPath)
	return exec(cmd)
end

--获取已安装Apkabi 包名
function Memory:getAbi(package)
	local cmd = string.format("dumpsys package %s | grep primaryCpuAbi" , package)
	return exec(cmd):match("primaryCpuAbi=(%S+)")
end

--获取内存区域的缺页状态
function Memory:isMemory(address , size)
	if self.pid == - 1 then error("pid is -1") end
	return self.mem.isMemory(address , self.pid , size)
end

--获取内存权限 --
function Memory:getMemoryPermission(address )
	if self.pid == - 1 then error("pid is -1") end
	--if self.arch ~= 64 then error("暂不支持32位进程")end
	return ffi.string(self.mem.getMemoryPermission(self.pid ,address))
end

--设置内存权限 rwxp / r-xp / rw-p / r--p
function Memory:setMemoryPermission(address ,perm )
	if self.pid == - 1 then error("pid is -1") end
	if self.arch ~= 64 then error("暂不支持32位进程")end
	return self.mem.setMemoryPermission(self.pid ,address,perm) == 0
end

--获取进程运行的架构 /pid 
--返回值 32 / 64
function Memory:getProcessArchitecture()
	if self.pid == - 1 then error("pid is -1") end
	return self.mem.getProcessArchitecture(self.pid )
end

--指定进程注入so文件
function Memory:injector(so_path)
	if self.pid == - 1 then error("pid is -1") end
	if self.arch ~= 64 then error("暂不支持32位进程")end
	return self.mem.injector(self.pid ,so_path)
end



--锁链 首地址 /链条
function Memory:fuckChain(address , chainArg)
	local success = nil
	local size = ffi.sizeof(typeMap.ui64)
	local buffer = ffi.new("char[?]" , size)
	local base = chainArg:match(".*()%+")
	local _end = chainArg:sub(base + 1 , #chainArg) 
	
	chainArg = chainArg:sub(1 , base - 1)
	for offset in chainArg:gmatch("([^+]+)") do
		
		success = self.mem.readMemory(self.pid , address + offset , buffer , size)
		
		if success then
			address = ffi.cast(typeMap.ui64 .. "*" , buffer)[0]
		end
		if address < 0 then
			--print(success , string.format("0x%X" , address) , offset) 处理b40000
			address = string.format("0x%X" , address):gsub("B40000" , "") + 0
		end
	end
	address = address + _end 
	
	return address
end


return Memory
