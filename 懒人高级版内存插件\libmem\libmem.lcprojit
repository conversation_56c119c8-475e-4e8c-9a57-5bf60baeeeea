<?xml version='1.0' encoding='UTF-8'?>
<lcprojit opt_openorclosebyvol_checked="1" show_exit_menu="1" opt_remove_imgui="0" opt_delncnn_checked="0" use_pluginservice="0" opt_openanrunscript_checked="0" vercode="1" vir_hei="0" plugin_path="" use_oldsnapmode="0" cpu_x86="1" custom_menu_name="我的菜单" opt_useoldnodeservice_checked="0" listen_port="0" use_simplelogwnd="0" opt_bootautorun_checked="0" opt_showscriptmainui_checked="1" opt_forceruninrootmode_checked="0" show_log_menu="1" opt_recorandsavelog_checked="1" opt_floatwnddock="1" use_oldfloatwnd="0" opt_notshowmainui_checked="0" is_acc_mode="0" vername="1.0.0" engine_type="3" icon="" opt_disableshowfloatwnd_checked="0" appname="libmem" crashautorestart="0" show_pause_menu="1" use_confusion="0" show_setting_menu="1" apiport="0" opt_runinrootmode_checked="1" pkgname="com.lr.libmem" vir_wid="0" lcprojversion="150" opt_logofillfloatwnd_checked="0" cpu_x86_64="1" use_pluginmainui="0" cpu_arm64="1" show_custom_menu="1" opt_delonnx_checked="0" projname="libmem" cpu_arm="1" opt_delnatural_checked="0">
 <lua entry="libmem.lua"/>
 <ui entry="libmem.ui"/>
 <rc entry="libmem.rc"/>
 <luaej/>
</lcprojit>
