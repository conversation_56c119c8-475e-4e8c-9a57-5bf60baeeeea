-- 高级版内存插件0.1.8瞬移脚本
-- 使用0.1.8版本的内存插件API

local plugin = require("mem")
local mem = plugin:new("mem.apk")

-- 配置
local 目标包名 = "ccz.locojoy.mini.mt34.joysdk"

-- 获取目标进程PID
mem:get_pid(目标包名)

-- 瞬移函数 - 使用0.1.8版本API
function 瞬移(x坐标, y坐标)
    print(string.format("开始瞬移到坐标: (%.1f, %.1f)", x坐标, y坐标))

    -- 获取libgame.so模块信息
    local 模块信息 = mem:getModuleInfo("libgame.so")
    if not 模块信息 or #模块信息 == 0 then
        print("❌ 获取libgame.so模块失败")
        return false
    end

    local 模块基址 = 模块信息[1].start
    print(string.format("模块基址: 0x%X", 模块基址))

    -- 计算BSS地址
    local bss地址 = 模块基址 + 0xD37684

    -- 读取BSS地址的值
    local 读取bss值, err = mem:read_memory(bss地址, mem.ui32)
    if not 读取bss值 then
        print("❌ 读取BSS地址失败: " .. (err or "未知错误"))
        return false
    end

    -- 确定基地址
    local 基地址 = 读取bss值 ~= 0 and 读取bss值 or bss地址
    print(string.format("基地址: 0x%X", 基地址))

    -- 检查进程架构并选择方法
    local 进程架构 = mem:getProcessArchitecture()
    local 最终地址

    if 进程架构 == 64 then
        -- 64位：使用锁链偏移功能
        print("使用64位锁链偏移功能")
        最终地址 = mem:fuckChain(基地址, "0x78+0x11C")
        if not 最终地址 or 最终地址 == 0 then
            print("锁链偏移失败，使用传统方法")
            local 偏移1, _ = mem:read_memory(基地址 + 0x78, mem.ui32)
            local 偏移2, _ = mem:read_memory(偏移1 + 0x11C, mem.ui32)
            最终地址 = 偏移2
        end
    else
        -- 32位：使用传统方法
        print("使用32位传统偏移方法")
        local 偏移1, err1 = mem:read_memory(基地址 + 0x78, mem.ui32)
        if not 偏移1 then
            print("❌ 读取第一级偏移失败: " .. (err1 or "未知错误"))
            return false
        end

        local 偏移2, err2 = mem:read_memory(偏移1 + 0x11C, mem.ui32)
        if not 偏移2 then
            print("❌ 读取第二级偏移失败: " .. (err2 or "未知错误"))
            return false
        end

        最终地址 = 偏移2
    end

    print(string.format("最终地址: 0x%X", 最终地址))

    -- 计算实际坐标值
    local x实际值 = x坐标 * 24
    local y实际值 = y坐标 * 16

    -- 修改X坐标
    local x结果 = mem:write_memory(最终地址 + 0x20 - 20, x实际值, mem.f32)
    if not x结果 then
        print("❌ 修改X坐标失败")
        return false
    end

    -- 修改Y坐标
    local y结果 = mem:write_memory(最终地址 + 0x20 - 16, y实际值, mem.f32)
    if not y结果 then
        print("❌ 修改Y坐标失败")
        return false
    end

    print(string.format("✅ 瞬移成功: (%.0f, %.0f)", x实际值, y实际值))

    -- 执行点击操作
    if _G.sleep then _G.sleep(1500) end
    if _G.tap then _G.tap(648, 362) end
    if _G.sleep then _G.sleep(500) end

    return true
end

-- 使用示例
print("=== 高级版内存插件0.1.8瞬移脚本 ===")
print("使用0.1.8版本API，支持64位系统和部分32位功能")
print("使用方法: 瞬移(x坐标, y坐标)")

-- 测试瞬移
瞬移(80, 50)
