
ffi.cdef[[
// 日志回调
typedef void (*LogCallback)(int level, const char* message);
void memsy_setLogCallback(LogCallback callback);
void memsy_removeLogCallback();

// 搜索结果结构体
typedef struct {
int count;
long* addresses;
} SearchResult;

// 基础功能
int memsy_getPID(const char* packageName);
int memsy_suspendProcess(int pid);
int memsy_resumeProcess(int pid);

// 搜索功能
SearchResult* memsy_searchFirst(int rangeType, int pid, const char* value, int valueType);
SearchResult* memsy_searchFilter(const SearchResult* prevResult, int pid, const char* value, int valueType);
SearchResult* memsy_searchOffset(const SearchResult* prevResult, int pid, const char* value, int valueType, int offset);
SearchResult* memsy_searchGroup(int rangeType, int pid, const char* group_search_str);

// 内存修改
int memsy_editMemory(const SearchResult* result, int pid, const char* value, int valueType, int maxCount);
int memsy_writeAddress(int pid, long address, const char* value, int valueType);
int memsy_writeMultipleAddresses(int pid, const long* addresses, const char** values, const int* valueTypes, int count);

//内存读取
int memsy_readAddress(int pid, long address, int valueType, void* outValue);


// 结果管理
int memsy_getResultCount(const SearchResult* result);
long memsy_getResultAddress(const SearchResult* result, int index);
void memsy_freeResult(SearchResult* result);

// 错误处理
const char* memsy_getLastError();
]]
function getSoPath(soname)
	local work = getWorkPath().."/../bin/界面/libs"
	local type = getCpuArch()
	if type == 0 then
		work = work .. "/x86/"..soname
	elseif type == 1 then
		work = work .. "/armeabi-v7a/"..soname
	elseif type == 2 then
		work = work .. "/arm64-v8a/"..soname
	else
		work = work .. "/x86_64/"..soname
	end
	return work
end


-- 2. 加载库
local libPath =getSoPath("/libmem.so")
local C = ffi.load(libPath)


-- ===================================================================
--                     MemsyTool: 面向对象的封装
-- ===================================================================

local MemsyTool = {}
MemsyTool.__index = MemsyTool
-- 构造函数
function MemsyTool.new(packageName)
	local self = setmetatable({} , MemsyTool)
	self.packageName = assert(packageName , "包名不能为空")
	self.pid = 0
	self.lastError = ""
	self.lastResult = nil
	
	-- 为SearchResult设置自动垃圾回收
	ffi.metatype("SearchResult" , {
		__gc = function(res)
			-- print(string.format("GC: 自动释放 SearchResult %p", res))
			C.memsy_freeResult(res)
		end
	})
	
	return self
end

-- 内部错误处理函数
function MemsyTool:_checkError()
	local err = C.memsy_getLastError()
	if err ~= nil and ffi.string(err) ~= "" then
		self.lastError = ffi.string(err)
		return true
	end
	return false
end

-- 基础API封装
function MemsyTool:getPID()
	self.pid = C.memsy_getPID(self.packageName)
	if self:_checkError() then return nil , self.lastError end
	return self.pid
end

function MemsyTool:suspend()
	if self.pid == 0 then return nil , "PID 未设置" end
	C.memsy_suspendProcess(self.pid)
	if self:_checkError() then return nil , self.lastError end
	return true
end

function MemsyTool:resume()
	if self.pid == 0 then return nil , "PID 未设置" end
	C.memsy_resumeProcess(self.pid)
	if self:_checkError() then return nil , self.lastError end
	return true
end

-- 搜索API封装
function MemsyTool:searchFirst(rangeType , value , valueType)
	if self.pid == 0 and not self:getPID() then return nil , self.lastError end
	
	self:suspend()
	local result = C.memsy_searchFirst(rangeType , self.pid , tostring(value) , valueType)
	self:resume()
	
	if self:_checkError() or result == nil then
		self.lastResult = nil
		return nil , self.lastError
	end
	self.lastResult = result
	return result
end

function MemsyTool:filter(value , valueType)
	if not self.lastResult then return nil , "没有可供过滤的搜索结果" end
	if self.pid == 0 and not self:getPID() then return nil , self.lastError end
	
	self:suspend()
	local result = C.memsy_searchFilter(self.lastResult , self.pid , tostring(value) , valueType)
	self:resume()
	
	if self:_checkError() or result == nil then
		self.lastResult = nil
		return nil , self.lastError
	end
	self.lastResult = result
	return result
end

-- 在 MemsyTool 中
function MemsyTool:groupSearch(rangeType , group_str)
	if self.pid == 0 and not self:getPID() then return nil , self.lastError end
	
	-- 直接调用强大的C++接口
	local result = C.memsy_searchGroup(rangeType , self.pid , group_str)
	
	if self:_checkError() or result == nil then
		self.lastResult = nil
		return nil , self.lastError
	end
	self.lastResult = result
	return result
end

function MemsyTool:offsetSearch(value , valueType , offset)
	if not self.lastResult then return nil , "没有可供偏移搜索的基地址" end
	if self.pid == 0 and not self:getPID() then return nil , self.lastError end
	
	self:suspend()
	local result = C.memsy_searchOffset(self.lastResult , self.pid , tostring(value) , valueType , offset)
	self:resume()
	
	if self:_checkError() or result == nil then
		self.lastResult = nil
		return nil , self.lastError
	end
	self.lastResult = result
	return result
end

-- 修改API封装
function MemsyTool:edit(value , valueType , maxCount)
	maxCount = maxCount or - 1 -- 默认为-1，修改所有
	if not self.lastResult then return nil , "没有可供修改的地址" end
	if self.pid == 0 and not self:getPID() then return nil , self.lastError end
	
	self:suspend()
	local count = C.memsy_editMemory(self.lastResult , self.pid , tostring(value) , valueType , maxCount)
	self:resume()
	
	if self:_checkError() or count < 0 then
		return nil , self.lastError
	end
	return count
end

function MemsyTool:write(address , value , valueType)
	if self.pid == 0 and not self:getPID() then return nil , self.lastError end
	
	self:suspend()
	local success = C.memsy_writeAddress(self.pid , address , tostring(value) , valueType)
	self:resume()
	
	if self:_checkError() or success == 0 then
		return nil , self.lastError
	end
	return true
end

function MemsyTool:read(address , valueType)
	if self.pid == 0 and not self:getPID() then return nil , self.lastError end
	
	-- 根据类型创建用于接收值的ffi数据结构
	local ffi_type , ffi_val
	if valueType == 0 then ffi_type , ffi_val = "int[1]" , ffi.new("int[1]")
	elseif valueType == 1 then ffi_type , ffi_val = "float[1]" , ffi.new("float[1]")
	elseif valueType == 2 then ffi_type , ffi_val = "double[1]" , ffi.new("double[1]")
	elseif valueType == 3 then ffi_type , ffi_val = "unsigned short[1]" , ffi.new("unsigned short[1]")
	elseif valueType == 4 then ffi_type , ffi_val = "unsigned char[1]" , ffi.new("unsigned char[1]")
	else
		return nil , "无效的数据类型"
	end
	
	-- 调用C函数
	local success = C.memsy_readAddress(self.pid , address , valueType , ffi_val)
	
	if success == 1 then
		return ffi_val[0] -- 返回读取到的值
	else
		self:_checkError()
		return nil , self.lastError
	end
end

-- 结果管理API封装
function MemsyTool.getResultCount(result)
	return C.memsy_getResultCount(result)
end

function MemsyTool.getResultAddresses(result)
	if not result or result == nil then return {} end
	local count = MemsyTool.getResultCount(result)
	local addresses = {}
	for i = 0 , count - 1 do
		table.insert(addresses , C.memsy_getResultAddress(result , i))
	end
	return addresses
end

function MemsyTool.memsy_setLogCallback()
	-- 日志封装
	local logCallback = ffi.cast("LogCallback" , function(level , message)
		local levelStr = (level == 3) and "DEBUG" or "ERROR"
		print(string.format("SO日志: [%s] %s" , levelStr , ffi.string(message)))
	end)
	C.memsy_setLogCallback(logCallback)
end


return MemsyTool
