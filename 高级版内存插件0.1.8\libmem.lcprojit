<?xml version='1.0' encoding='UTF-8'?>
<lcprojit use_oldsnapmode="0" vir_wid="0" show_log_menu="1" crashautorestart="0" opt_delncnn_checked="1" icon="" engine_type="3" use_confusion="0" projname="libmem" cpu_arm64="0" listen_port="0" opt_recorandsavelog_checked="1" use_oldfloatwnd="0" use_simplelogwnd="0" vercode="2" custom_menu_name="我的菜单" opt_showscriptmainui_checked="0" opt_floatwnddock="1" plugin_path="" opt_delnatural_checked="1" pkgname="bin.mem" vir_hei="0" show_exit_menu="1" show_pause_menu="1" opt_useoldnodeservice_checked="0" opt_remove_imgui="1" opt_disableshowfloatwnd_checked="0" opt_openorclosebyvol_checked="1" opt_forceruninrootmode_checked="80" is_acc_mode="0" opt_logofillfloatwnd_checked="0" appname="libmem" apiport="0" show_custom_menu="1" use_pluginservice="0" use_pluginmainui="0" opt_delonnx_checked="1" vername="0.0.2" cpu_arm="1" cpu_x86="1" opt_runinrootmode_checked="0" show_setting_menu="1" cpu_x86_64="0" opt_notshowmainui_checked="0" lcprojversion="150" opt_bootautorun_checked="0" opt_openanrunscript_checked="0">
 <lua entry="libmem.lua"/>
 <ui entry="libmem.ui"/>
 <rc entry="libmem.rc"/>
 <luaej/>
</lcprojit>
